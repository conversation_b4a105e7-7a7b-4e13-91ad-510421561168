import { HeroSection } from '@modules/home/<USER>/HeroSection';
import { BenefitSection } from '@modules/home/<USER>';
import { TestimonialSection } from '@modules/home/<USER>';
import { HomeLayout } from '@/components/layout/home/<USER>';
import { NavigationSection } from '@modules/home/<USER>/NavigationSection';
import { CategorySection } from '@modules/home/<USER>/CategorySection';
import { FeaturedProductsSection } from '@modules/home/<USER>/FeaturedProductsSection';
import { CategoryProductsSection } from '@/modules/home/<USER>/CategoryProductsSection';
import { PromotionalProductsSection } from '@/modules/home/<USER>/PromotionalProductsSection';
import { NavigationPath } from '@/routes/route-paths';
import { BiSolidStar, BiSolidOffer } from 'react-icons/bi';

export default function HomePage() {
  return (
    <HomeLayout navigationSection={<NavigationSection />}>
      <HeroSection />
      <CategorySection />
      <FeaturedProductsSection />
      <CategoryProductsSection
        title='Sản phẩm mới'
        icon={<BiSolidStar className='text-2xl' />}
        categoryId={1}
        categoryPath={`${NavigationPath.PRODUCTS}?category=1`}
        limit={9}
        isNewProducts={true}
      />
      <PromotionalProductsSection
        title='Khuyến mãi'
        icon={<BiSolidOffer className='text-2xl' />}
        categoryPath={`${NavigationPath.PRODUCTS}?promotion=true`}
        limit={9}
      />
      <BenefitSection />
      <TestimonialSection />
    </HomeLayout>
  );
}
