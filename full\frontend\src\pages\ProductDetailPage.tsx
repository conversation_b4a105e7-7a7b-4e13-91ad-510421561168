import { createRoute } from "@tanstack/react-router";
import {
  ShoppingCart,
  Truck,
  Shield,
  RefreshCw,
  Plus,
  Minus,
} from "lucide-react";
import {
  calculateDiscountPercent,
  formatVietnameseCurrency,
} from "@lib/utils/currencyUtils";
import { useEffect, useState, useRef } from "react";
import { useCart } from "@/context/CartContext";
import { mainLayoutRoute } from "@/routes";
import { getProductDetail } from "@lib/api";
import { ProductData } from "@lib/api/product/types";

export const Route = createRoute({
  getParentRoute: () => mainLayoutRoute,
  path: "/products/$id",
  component: ProductDetailPage,
});

export default function ProductDetailPage() {
  // Helper function to check if description is in table format
  const isDescriptionTableFormat = (description: string): boolean => {
    if (!description) return false;

    // Check if at least one line matches the format "number. key: value"
    // or at least one line matches "number. section" with content following it
    const lines = description.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.match(/^\d+\./) && (line.includes(':') || (i < lines.length - 1 && lines[i + 1].trim()))) {
        return true;
      }
    }
    return false;
  };

  // Helper function to get content for a section
  const getSectionContent = (description: string, startIndex: number): string => {
    const lines = description.split('\n');
    const contentLines: string[] = [];

    for (let i = startIndex + 1; i < lines.length; i++) {
      const line = lines[i];
      // Stop if we hit another numbered section
      if (line.match(/^\d+\./)) break;
      if (line.trim()) contentLines.push(line.trim());
    }

    return contentLines.join('\n');
  };

  const { id } = Route.useParams();
  const { addItem, cartIconRef } = useCart();
  const [quantity, setQuantity] = useState(1);

  const [product, setProduct] = useState<ProductData>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const productImageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  // Load product data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Load product data
        const product = await getProductDetail(Number(id));

        if (!product) {
          throw new Error("Product not found");
        }

        setProduct({
          ...product,
          id: product.id.toString(),
          title: product.name,
          thumbnail: product.image_url || "/images/image.png",
          price: product.price,
          discountedPrice: product.discount_price,
          isSale: Boolean(product.discount_price),
          isNew: false
        });
      } catch (error) {
        console.error("Failed to load product data:", error);
        const message =
          error instanceof Error
            ? error.message
            : "Failed to load product data";
        setError(message);
        setProduct(undefined);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [id]);

  const incrementQuantity = () => setQuantity((prev) => prev + 1);
  const decrementQuantity = () =>
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1));

  const handleAddToCart = () => {
    if (!product) return;
    addItem(product, quantity, productImageRef, cartIconRef);
  };

  if (isLoading) {
    return <div className="container py-8 text-center">Đang tải...</div>;
  }

  if (error) {
    return (
      <div className="container py-8 text-center text-red-600">{error}</div>
    );
  }

  if (!product) {
    return (
      <div className="container py-8 text-center text-red-600">
        Không tìm thấy sản phẩm
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Breadcrumbs */}
      <nav className="text-sm text-muted-foreground mb-8">
        <ol className="flex items-center space-x-2">
          <li>
            <a href="/" className="hover:text-primary">
              Trang chủ
            </a>
          </li>
          <li>/</li>
          <li>
            <a href="/products" className="hover:text-primary">
              Sản phẩm
            </a>
          </li>
          <li>/</li>
          <li className="text-primary font-medium truncate max-w-[200px]">
            {product.title}
          </li>
        </ol>
      </nav>

      {/* Product Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
        {/* Left - Product Image */}
        <div className="relative">
          <div className="aspect-square rounded-xl overflow-hidden bg-secondary mb-4">
            <img
              ref={productImageRef}
              src={product.thumbnail}
              alt={product.title}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Sale Badge */}
          {product.isSale && product.discountedPrice && (
            <div className="absolute top-4 left-4 bg-destructive text-destructive-foreground px-3 py-1 text-sm font-medium rounded-full">
              -
              {calculateDiscountPercent(product.price, product.discountedPrice)}
              %
            </div>
          )}

          {/* New Badge */}
          {product.isNew && (
            <div className="absolute top-4 right-4 bg-emerald-500 text-white px-3 py-1 text-sm font-medium rounded-full">
              Mới
            </div>
          )}
        </div>

        {/* Right - Product Info */}
        <div className="flex flex-col">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">
            {product.title}
          </h1>

          {/* Price */}
          <div className="flex items-center gap-3 mt-2 mb-6">
            {product.discountedPrice ? (
              <>
                <span className="text-2xl font-bold text-red-500">
                  {formatVietnameseCurrency(product.discountedPrice)}
                </span>
                <span className="text-muted-foreground line-through text-lg">
                  {formatVietnameseCurrency(product.price)}
                </span>
                <span className="bg-destructive/10 text-destructive text-sm font-medium px-2 py-1 rounded">
                  Tiết kiệm{" "}
                  {formatVietnameseCurrency(
                    product.price - product.discountedPrice
                  )}
                </span>
              </>
            ) : (
              <span className="text-2xl font-bold text-red-500">
                {formatVietnameseCurrency(product.price)}
              </span>
            )}
          </div>
          <div className="flex items-center mb-6">
            <span className="mr-4 font-medium">Số lượng:</span>
            <div className="flex items-center border px-1 rounded-md overflow-hidden">
              <button
                onClick={decrementQuantity}
                className="p-2 hover:bg-gray-300 rounded-sm transition-colors duration-200"
                aria-label="Giảm số lượng"
              >
                <Minus className="w-4 h-4" />
              </button>

              <span className="w-12 text-center py-2 font-medium">
                {quantity}
              </span>

              <button
                onClick={incrementQuantity}
                className="p-2 hover:bg-gray-300 rounded-sm transition-colors duration-200"
                aria-label="Tăng số lượng"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Add to Cart */}
          <div className="flex flex-wrap gap-4 mb-8">
            <button
              onClick={handleAddToCart}
              className="flex-1 px-6 py-3 text-white rounded-md shadow-sm bg-royal-blue hover:bg-royal-blue/90 flex items-center justify-center transition-colors duration-200"
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              <span>Thêm vào giỏ hàng</span>
            </button>
          </div>

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Truck className="w-5 h-5 text-primary mt-0.5" />
              <div>
                <p className="font-medium">Giao hàng toàn quốc</p>
                <p className="text-sm text-muted-foreground">
                  Nhận hàng trong vòng 2-3 ngày
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-primary mt-0.5" />
              <div>
                <p className="font-medium">Cam kết chính hãng</p>
                <p className="text-sm text-muted-foreground">
                  100% sản phẩm chính hãng
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <RefreshCw className="w-5 h-5 text-primary mt-0.5" />
              <div>
                <p className="font-medium">Đổi trả trong 7 ngày</p>
                <p className="text-sm text-muted-foreground">
                  Nếu sản phẩm có vấn đề
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Information Table */}
      <div className="mt-12">
        <div className="bg-white rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">THÔNG TIN SẢN PHẨM</h2>
          <div className="overflow-hidden">
            <table className="w-full border-collapse">
              <tbody>
                {isDescriptionTableFormat(product.description) && product.description.split('\n').map((line: string, index: number) => {
                    if (!line.trim()) return null;

                    const parts = line.split(':');
                    if (parts.length === 2) {
                      // Handle simple key-value pairs
                      const [key, value] = parts.map(part => part.trim());
                      if (key.match(/^\d+\./)) { // Remove numbering if present
                        const cleanKey = key.replace(/^\d+\./, '').trim();
                        return (
                          <tr key={index} className="border-b border-black last:border-b-0">
                            <td className="font-bold bg-gray-100 text-black p-4 w-1/4 border border-black">{cleanKey}</td>
                            <td className="p-4 text-black bg-white border border-black">{value}</td>
                          </tr>
                        );
                      }
                    } else if (line.match(/^\d+\./)) {
                      // Handle headers without values (e.g., "6. Mô Tả Sản Phẩm")
                      const header = line.replace(/^\d+\./, '').trim();
                      const content = getSectionContent(product.description, index);
                      if (content) {
                        return (
                          <tr key={index} className="border-b border-black last:border-b-0">
                            <td className="font-bold bg-gray-100 text-black p-4 w-1/4 border border-black">{header}</td>
                            <td className="p-4 text-black bg-white border border-black">
                              <div className="whitespace-pre-line">
                                {content.split('\n').map((paragraph, pIndex) => (
                                  <p key={pIndex} className="mb-2 last:mb-0">{paragraph}</p>
                                ))}
                              </div>
                            </td>
                          </tr>
                        );
                      }
                    }
                    return null;
                  })}
                </tbody>
              </table>
            </div>
            {!isDescriptionTableFormat(product.description) && (
              <div className="py-4 text-black whitespace-pre-line">
                {product.description}
              </div>
            )}
        </div>
      </div>

      {/* Product Details */}
      <div className="mt-12">
        <div className="bg-white rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">ĐẶC ĐIỂM SẢN PHẨM</h2>
          <div className="space-y-4">
          {product.specifications && product.specifications !== "" && (
            <div className="whitespace-pre-line text-muted-foreground">
              {product.specifications}
            </div>
          )}
          </div>
        </div>
      </div>
    </div>
  );
}
